const mix = require("laravel-mix");

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js("resources/js/app.js", "public/js")
    .sass("resources/sass/fontawesome.scss", "public/css")
    .copy(
        "node_modules/@fortawesome/fontawesome-free/webfonts",
        "public/webfonts"
    )
    .copy("resources/images", "public/images")
    .styles("resources/css/normalize.css", "public/css/normalize.css")
    .postCss("resources/css/app.css", "public/css", [require("tailwindcss")])
    .js(
        "resources/js/demand/setting/dem-setting/dem-setting.js",
        "public/js/demand/setting/dem-setting"
    )
    .js(
        "resources/js/demand/setting/database-setting/database-setting.js",
        "public/js/demand/setting/database-setting"
    )
    .js(
        "resources/js/demand/setting/permission-setting/permission-setting.js",
        "public/js/demand/setting/permission-setting"
    )
    .js(
        "resources/js/demand/setting/notify-setting/notify-setting.js",
        "public/js/demand/setting/notify-setting"
    )
    .js("resources/js/demand/submit/submit.js", "public/js/demand/submit")
    .js("resources/js/demand/signing/signing.js", "public/js/demand/signing")
    .js("resources/js/demand/query/query.js", "public/js/demand/query")
    .js("resources/js/par/setting/setting.js", "public/js/par/setting")
    .js("resources/js/par/reserve/reserve.js", "public/js/par/reserve")
    .js(
        "resources/js/par/my-reserves/my-reserves.js",
        "public/js/par/my-reserves"
    )
    .js(
        "resources/js/par/permission-setting/permission-setting.js",
        "public/js/par/permission-setting"
    )
    .js(
        "resources/js/explorer/file-lib/file-lib.js",
        "public/js/explorer/file-lib"
    )
    .js(
        "resources/js/explorer/permission-settings/permission-settings.js",
        "public/js/explorer/permission-settings"
    )
    .webpackConfig({
        resolve: {
            alias: {
                "@": path.resolve("resources/js"),
            },
        },
    })
    .sourceMaps()
    .version();
