name: Deploy
run-name: ${{ github.actor }} is testing out GitHub Actions 🚀

on:
    push:
        branches:
            - dev-master

jobs:
    deploy-project:
        env:
            DEPLOY_PATH: /var/www/html
            PHP_EXEC: /usr/bin/php8.2
            COMPOSER_EXEC: /usr/local/bin/composer
        runs-on: ap-dev-as
        steps:
            - uses: actions/checkout@v4

            - name: 系統資訊與資源庫檔案
              run: |
                  echo "User: $(whoami)"
                  echo "Shell: $SHELL"
                  echo "Home directory: $HOME"
                  id
                  env
                  ls ${{ github.workspace }}

            - name: 依據根目錄下的 ".node-version" 或 ".nvmrc" 檔案安裝並使用NodeJS版本
              run: |
                  FNM_PATH="/home/<USER>/.local/share/fnm"
                  export PATH="$FNM_PATH:$PATH"
                  eval "`fnm env`"
                  fnm use --install-if-missing

            - name: 檢查 pnpm 是否存在，如果不在自動安裝
              run: |
                  if ! command -v pnpm &> /dev/null
                  then
                      echo "pnpm could not be found"
                      npm install -g pnpm
                  else
                      echo "pnpm is installed"
                  fi

            - name: 安裝依賴
              run: npx pnpm install

            - name: 編譯前端資源
              run: npx pnpm run build

            - name: 安裝 PHP 依賴
              run: COMPOSER_ALLOW_SUPERUSER=1 $PHP_EXEC $COMPOSER_EXEC install

            - name: 部署專案
              run: |
                  pwd
                  echo "create deploy path: $DEPLOY_PATH"
                  mkdir -p $DEPLOY_PATH

                  echo "rm -rf $DEPLOY_PATH/node_modules $(pwd)/node_modules"
                  rm -rf $DEPLOY_PATH/node_modules $(pwd)/node_modules

                  echo "rsync -av --no-perms --no-owner --no-group --remove-source-files --exclude='.git' --exclude='.vscode' --exclude='.gitea' ./ $DEPLOY_PATH/"
                  rsync -av --no-perms --no-owner --no-group --remove-source-files --exclude='.git' --exclude='.vscode' --exclude='.gitea' ./ $DEPLOY_PATH/

                  echo "deploy path ($DEPLOY_PATH) successfully !"
                  chown -R www-data:www-data $DEPLOY_PATH
                  echo "修改使用者與群組: $DEPLOY_PATH (www-data:www-data)"

            - name: 重啟排程
              run: |
                  echo "重啟排程 ..."
                  ps aux | grep "artisan queue:work" | grep -i asap | grep dev | awk '{print $2}' | xargs -r kill || true
