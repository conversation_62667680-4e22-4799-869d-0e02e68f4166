<?php

namespace App\Modules\explorer\Controllers;

use App\Modules\Demand\Models\FuncAuth;
use App\Modules\Demand\Services\UploadNotifyService;
use App\Http\Controllers\Controller;
use App\Jobs\UploadToServiceJob;
use App\Traits\UploadFile;
use Exception;
use GuzzleHttp\Exception\BadResponseException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use GuzzleHttp\Client;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExplorerController extends Controller
{
    use UploadFile;
    protected $company_id, $user_id, $file_url, $client;
    public function __construct()
    {
        $this->company_id = (int) Session::get('CompanyId');
        $this->user_id = (int) Session::get('employee_id');
        $this->file_url = config("app.services_file_api_url");
        $this->client = new Client();

    }

    public function index()
    {
        $rule = \App\Modules\explorer\Models\OcrFileRule::query()
            ->where('employee_id', $this->user_id)
            ->first();

        $ruleName = '';
        $ruleId = '';
        if ($rule && $rule->enable_id) {
            $enable_id = $rule->enable_id;

            $enableRule = $rule->rules->first(function ($value) use ($enable_id) {
                return $value['id'] == $enable_id;
            });
            if ($enableRule) {
                $ruleId = $enableRule['id'];
                $ruleName = $enableRule['title'];
            }
        }

        // createPermission true/false
        $createPermission = FuncAuth::query()
            ->where('company_id', $this->company_id)
            ->where('type', 'explorer')
            ->whereJsonContains('payload->create_white', [$this->user_id])->exists();
        // dd($createPermission);
        return view('explorer.explorer', ['ruleName' => $ruleName, 'ruleId' => $ruleId, 'createPermission' => $createPermission]);
    }

    public function fetchRoot()
    {
        $url = $this->urlScope($this->file_url . "/index");

        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());

        if ($response->getStatusCode() != 200) {
            return response()->json(['msg' => $result->msg], 400);
        }

        return response()->json($result, 200);
    }
    public function testFile()
    {
        $path = '/public/explorer/temp/1699515620_88a03c338af98f44373e3d6c9de3cc01.pdf';

        $name = 'report.pdf';
        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="report.pdf"'
        ];
        return Storage::download($path, $name, $header);

    }
    public function getFileInfo(Request $request)
    {
        if (!$request->has(['id', 'type']))
            return response('unsupported', 400);

        $id = $request->input('id');
        // 操作類型 ("raad" or "download")
        $type = $request->input('type');
        $isOldVersion = $request->input('isOldVersion');
        $url = $this->urlScope($this->file_url . '/file-info') . "&id=$id&type=$type&isOldVersion=$isOldVersion";

        try {
            $response = $this->client->get($url, ['stream' => true]);
            $body = $response->getBody();
            $response = new StreamedResponse(function () use ($body) {
                while (!$body->eof()) {
                    echo $body->read(1024);
                }
            });
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            \Log::info($msg);
            return response()->json(['msg' => $msg], 400);
        }


        return $response;
    }

    public function fetchCurrentDir($folderId)
    {
        if (!isset($folderId))
            return response('unsupported', 400);

        $url = $this->urlScope($this->file_url . "/current/$folderId");
        // 當folderId = 0,為取得scope 的root
        // if ($folderId == 0)
        //     $url = $this->urlScope($this->file_url . "/index");
        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = (string) $response->getBody();
        $result = json_decode($result);
        if ($response->getStatusCode() != 200) {
            return response($result->msg, 400);
        }

        return response()->json($result->data, 200);
    }
    public function fetchAllDir()
    {
        $url = $this->urlScope($this->file_url . '/dir');

        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = (string) $response->getBody();
        $result = json_decode($result);
        if ($response->getStatusCode() != 200) {
            return response($result->msg, 400);
        }
        return response()->json($result->data, 200);
    }

    public function createDir(Request $request)
    {
        $validator = Validator::make(
            $request->input("newFolders"),
            [
                '*.name' => 'required',
                '*.path' => 'required|array',
                '*.parentId' => 'required',
            ]
            ,
            [
                '*.*.*' => 'unsupported',
            ]
        );

        if ($validator->fails()) {
            return response()->json(['msg' => $validator->errors()->first()], 400);
        }

        $url = $this->urlScope($this->file_url . '/folder');
        $dir = [
            'newFolders' => $request->input("newFolders"),
        ];

        try {
            $response = $this->client->post($url, ['json' => $dir]);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        return response(201);
    }

    public function delete(Request $request)
    {
        $ids = $request->ids;
        if (!$request->has('ids') && ((is_int($ids) || is_array($ids))))
            return response('unsupported', 400);

        $url = $this->urlScope($this->file_url.'/delete');
        // dd($url);
        try {
            $response = $this->client->post($url, ['json' => ['ids' => $ids]]);
        } catch (BadResponseException $e) {

            // dd(json_decode($e->getResponse()->getBody()->getContents()));
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }

        return response(200);
    }

    public function rename(Request $request, $id)
    {
        if (!isset($id) || !$request->has('name'))
            return response('unsupported', 400);

        $url = $this->urlScope($this->file_url . '/rename/' . $id);
        $name = $request->input('name');
        try {
            $response = $this->client->put($url, ['json' => ['name' => $name]]);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());

        return response(200);
    }
    public function copy(Request $request)
    {
        if (!$request->has(['targetFolderId','ids']))
            return response('unsupported', 400);

        $ids = $request->ids;
        $url = $this->urlScope($this->file_url . '/batch/action') . '&action=copy';
        $targetFolderId = $request->input('targetFolderId');
        try {
            $response = $this->client->put($url, [
                'json' => [
                    'ids' => $ids,
                    'targetFolderId' => $targetFolderId
                ]
            ]);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());

        return response(200);
    }
    public function move(Request $request)
    {
        if (!$request->has(['targetFolderId','ids']))
            return response('unsupported', 400);
        $ids = $request->ids;
        $url = $this->urlScope($this->file_url . '/batch/action') . '&action=move';
        $targetFolderId = $request->input('targetFolderId');
        try {
            $response = $this->client->put($url, ['json' => ['ids' => $ids, 'targetFolderId' => $targetFolderId]]);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());

        return response(200);
    }
    public function uploadFile(Request $request)
    {
        try {
            // 檢查
            $file = $request->file();
            $validator = Validator::make(
                $request->all(),
                [
                    'file' => 'required|mimes:pdf,xls,xlsx,xlsm,csv,doc,docx,jpg,jpeg,png|max:10240',
                    'key' => 'required',
                ]
                ,
                [
                    'file.required' => '必須上傳檔案!',
                    'file.mimes' => '檔案類型不正確，需是pdf,word,excel,image',
                    'file.max' => '超過檔案大小',
                    'key.required' => 'unsupported',
                ]
            );

            if ($validator->fails()) {
                \Log::info(__FUNCTION__ . ' : ' . __LINE__);

                return response()->json(['msg' => $validator->errors()->first()], 400);
            }

            // 檔案丟到temp
            // storage_path('app/public/explorer/temp')
            $fileOriginalName = $file['file']->getClientOriginalName();
            // 考慮會有其他功能，不是直接取得檔案的，所以一律丟到temp去
            $fileName = $this->uploadTrait($file['file'], '/public/explorer/temp');
            // 上傳編號
            $patchNo = $request->input('key');

            // 丟到背景
            UploadToServiceJob::dispatch($fileOriginalName, $fileName, $this->company_id, $this->user_id, $patchNo);
            // UploadToServiceJob::dispatchNow($fileOriginalName, $fileName, $this->company_id, $this->user_id, $patchNo);

            return response(201);
        } catch (Exception $e) {
            \Log::error($e);
            (new UploadNotifyService)->updateUploadResponse($patchNo, 0, $fileOriginalName);
        }
    }


    public function search(Request $request)
    {
        if (!$request->has('keyword'))
            return response('unsupported', 400);

        $keyword = $request->input('keyword');
        $url = $this->urlScope($this->file_url . "/search") . "&keyword=$keyword";

        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());
        return response()->json($result->data, 200);
    }
    public function getfileVersionList($fileId)
    {
        $url = $this->urlScope($this->file_url . "/version/$fileId");
        try {
            $response = $this->client->get($url);
        } catch (BadResponseException $e) {
            $msg = json_decode($e->getResponse()->getBody()->getContents())->msg;
            return response()->json(['msg' => $msg], 400);
        }
        $result = json_decode((string) $response->getBody());
        return response()->json($result->data, 200);
    }
}
