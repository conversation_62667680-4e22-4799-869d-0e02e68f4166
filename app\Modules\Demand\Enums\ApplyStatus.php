<?php

namespace App\Modules\Demand\Enums;

//資料庫欄位叫做apply_status，其代表的含意應該是審核人當初的審核類別
enum ApplyStatus: int
{
    case UNAUDITED = 0;
    case REVIEWING = 1;
    case AGREED = 2;
    case REJECTED = 3;
    case REVIEW = 4;
    case COUNTERSIGN = 5;
    case RETURN = 6;
    case RETURN_TO_APPLICANT = 7;

    public function getLabel(): string
    {
        return match ($this) {
            self::UNAUDITED => '未審核',
            self::REVIEWING => '審核中',
            self::AGREED => '同意',
            self::REJECTED => '駁回',
            self::REVIEW => '再審',
            self::COUNTERSIGN => '會簽',
            self::RETURN => '退回',
            self::RETURN_TO_APPLICANT => '退回至申請人',
        };
    }
}
