import './bootstrap.js';

const app = new Vue({
    el: "#app",
    data: {
        funcMenu: null,
        sideMenuActive: 1,
        isShow: "",
        user: null,
        notifyURL: "/api/demand/notify",
        notifies: [],
        showOptions: false,
    },
    mounted() {
        this.funcMenu = functionMenu;
        let last_section = location.href.replace(/\/+$/, "").split("/").pop();
        this.funcMenu.forEach((fm) => {
            if (fm.last_section == last_section) {
                fm.active = 1;
                fm.drop = 1;
            } else {
                if ("dropDown" in fm) {
                    fm.dropDown.forEach((fmd) => {
                        if (fmd.last_section == last_section) {
                            fm.active = 1;
                            fmd.active = 1;
                            fm.drop = 1;
                        } else {
                            if ("dropDown" in fmd) {
                                fmd.dropDown.forEach((fmdD) => {
                                    if (fmdD.last_section == last_section) {
                                        fm.drop = 1;
                                        fmd.drop = 1;
                                        fmdD.active = 1;
                                        fmdD.drop = 1;
                                    }
                                });
                            }
                        }
                    });
                }
            }
        });
        // this.getNotify();
    },
    watch: {},
    methods: {
        getUserData() {
            axios
                .get("/api/demand/user/data")
                .then((response) => {
                    this.user = response.data ?? null;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        getNotify() {
            axios
                .get(this.notifyURL)
                .then((response) => {
                    this.notifies = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        putNotify(id) {
            axios
                .put(this.notifyURL + "/read-at", { id: id })
                .then((response) => {})
                .catch((error) => {
                    console.error(error);
                });
        },
        showPerson() {
            this.isShow == "person"
                ? (this.isShow = "")
                : (this.isShow = "person");
            if (this.user == null) {
                this.getUserData();
            }
        },
        showNotify() {
            this.isShow == "notify"
                ? (this.isShow = "")
                : (this.isShow = "notify");
        },
        clearNotify() {
            axios
                .delete(this.notifyURL)
                .then((response) => {})
                .catch((error) => {
                    console.error(error);
                });
            this.notifies = [];
            this.getNotify();
        },
    },
});

