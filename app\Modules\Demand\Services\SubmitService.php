<?php

namespace App\Modules\Demand\Services;

use App\Events\DemandUpdated;
use App\Handlers\RefreshMaterializedViewDemandQuery;
use App\Modules\Demand\Controllers\NotificationController;
use App\Modules\Demand\Controllers\RoleController;
use App\Modules\Demand\Controllers\UploadController;
use App\Modules\Demand\Models\Demand;
use App\Modules\Demand\Models\DemandLayout;
use App\Modules\Demand\Repositories\DemandQueryRepository;
use App\Modules\Demand\Models\DemandLog;
use App\Modules\Demand\Models\DemandGroup;
use App\Modules\Demand\Models\Employee;
use App\Modules\Demand\Models\OrgUnit;
use App\Modules\Demand\Models\OrgUnitMember;
use App\Modules\Demand\Models\Code;
use App\Modules\Demand\Models\CustomList;
use App\Traits\ApiResponder;
use App\Traits\FormatDate;
use App\Traits\GetColumnValue;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
class SubmitService
{
    // protected $company_id;
    // protected $user_id;
    // protected $demandQuery;
    // protected $codeTable;
    protected $timezone;
    use FormatDate, ApiResponder, GetColumnValue;


    public function __construct()
    {
        // $this->timezone = Session::get('timezone','asia\taipei');
    }
    /**
     * Summary of createDemand
     * @param mixed $required ['employee_id','CompanyId','timezone']
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function createDemand($required, Request $request)
    {
        $Role = new RoleController;
        $Notify = new NotificationController;
        $userId = $required['employee_id'];
        $CompanyId = $required['CompanyId'];
        $timezone = $required['timezone'];
        try {
            DB::beginTransaction();
            $id = $request->get('id');
            $layout_original_id = $request->get('layout_original_id');
            $requestForms = $request->get('forms');
            if ($requestForms && $id && $layout_original_id) {
                $layout = DemandLayout::find($id);
                $roles = $request->get('sign_role');

                // 檢查
                if (empty($roles))
                    return 0;
                $noSignRoles = collect($roles)->every(function ($role) {
                    return !empty($role['isRemoved']);
                });
                if ($noSignRoles)
                    return $this->errorResponse('審核關卡不得為空', 422);


                // 整理被移除的簽核關卡(申請人移除自行指定人員)
                $roles = collect($roles)->reduce(function ($tempRoles, $role) {
                    if (empty($role['isRemoved'])) {
                        $role['id'] = $tempRoles->count() + 1;
                        $tempRoles->push($role);
                    }

                    return $tempRoles;
                }, collect())->toArray();

                //$roles = $roles->toArray();
                $requestFilterRoles = $request->input('filter_roles') ?? [];
                // 金額判斷流程，確認是否增加/減少 審核關卡
                foreach ($requestFilterRoles as $key => &$filter_role) {
                    $filter_role['apply_status'] = 0;
                    $filter_role['raw_sign_off'] = true;

                    //審核人絕對職等
                    if ($filter_role['type'] == 0 && $filter_role['self_applicant'] == false) {
                        $filter_role['role_id'] = $Role->findLevleManager($userId, $filter_role['level']);
                    }
                }

                foreach ($roles as $key => $role) {
                    $roles[$key]['apply_status'] = 0;
                    //審核人絕對職等
                    if ($role['type'] == 0 && $role['self_applicant'] == false) {
                        $roles[$key]['role_id'] = $Role->findLevleManager($userId, $role['level']);
                    }
                }
                if ($request->has('is_child') && $request->get('is_child')) {
                    foreach ($roles as $key => $role) {
                        if ($role['type'] == 0) {
                            unset($roles[$key]);
                        }
                    }
                    $roles = array_values($roles);
                }
                //設第一關開始
                $signing = 1;
                //加入會簽
                if ($request->has('counterRoles')) {
                    $counterRoles = $request->get('counterRoles');
                    //先加入換簽完回本人的關卡
                    $roles = Arr::prepend(
                        $roles,
                        [
                            'child' => false,
                            'type' => 0,
                            'document' => false,
                            'self_name' => Employee::find(intval($userId))->orgs->pluck('payload.name')->first(),
                            'countersigned' => true,
                            'role_id' => intval($userId),
                            'timestamp' => '',
                            'apply_status' => 0,
                            'to_counter' => 2
                        ]
                    );
                    //加入會簽
                    foreach ($counterRoles as &$counterRole) {
                        $counterRole['apply_status'] = 1;
                        // $roles = Arr::prepend($roles, $counterRole);
                    }
                    $roles = array_merge($counterRoles, $roles);
                } else {
                    $roles[0]['apply_status'] = $signing;
                }
                foreach ($roles as $index => $singRole) {
                    $roles[$index]['id'] = $index + 1;
                    $roles[$index]['raw_sign_off'] = true;
                }
                ;


                //發通知
                $layoutName = $layout->name;
                $Notify->createNotify($roles[0]['role_id'], $layoutName, $signing);

                //填寫後的表單
                $forms = collect();
                $customList = [];
                collect($requestForms)->each(function ($cols, $formIndex) use ($forms, &$customList, $userId) {

                    $cols = collect($cols)->transform(function ($col, $columnIndex) use ($formIndex, &$customList, $userId) {
                        if ($col['type'] == 'customList') {
                            array_push(
                                $customList,
                                [
                                    'demand_id' => 0,
                                    'form_index' => $formIndex,
                                    'column_index' => $columnIndex,
                                    'payload' => json_encode(['form_setting' => $col['form_setting']]),
                                    'list' => json_encode($col['custom_list']),
                                    'created_by' => $userId,
                                    'updated_by' => $userId,
                                    'created_at' => now(),
                                ]
                            );
                            unset($col['form_setting']);
                            unset($col['custom_list']);
                        }
                        return $col;
                    });
                    $forms->push(['columns' => $cols]);
                });

                $user = Employee::where('company_id', $CompanyId)
                    ->where('id', $userId)
                    ->with('orgs')
                    ->first();
                $userOrgId = $user->orgs->pluck('id')->first();

                // 摘要
                $summaryColId = $layout->payload->get('summaryColId');
                if (isset($summaryColId)) {
                    $summaryColumn = null;
                    foreach ($forms[0]['columns'] as $col) {
                        if ($col["id"] === $summaryColId) {
                            $summaryColumn = $col;
                            break;
                        }
                    }
                    $summaryValue = $summaryColumn ? $this->getValue($summaryColumn) : "";
                    // $tempColumn = $forms[0]['columns'][2 - 1];
                    // $summaryValue =   $this->getValue($tempColumn);
                } else
                    $summaryValue = '';

                $payload = (object) [
                    'is_child' => $request->has('is_child') && $request->get('is_child') ? true : false,
                    'sign_roles' => $roles,
                    'forms' => $forms,
                    'status' => 1,
                    'layout_original_id' => $layout_original_id,
                    'company_id' => $CompanyId,
                    'layout_name' => $layoutName,
                    'filter_roles' => $requestFilterRoles,
                    'remarks' => $layout->payload['remarks'],
                    'created_org_id' => $userOrgId ?? 0, //申請者部門id
                    'summaryValue' => $summaryValue,
                    'unique_key' => $layout->payload->get('unique_key') ?? '',
                    'applicant' => $request['applicant']
                ];
                if ($request->has('counterRoles')) {
                    $payload->counterRoleIds = Arr::pluck($counterRoles, 'role_id');
                }
                if ($request->has('is_child') && $request->get('is_child')) {
                    $payload->parent = $request->get('parent');
                }

                $no = $this->makeDemandNo($timezone, $layout);
                $DemandModel = Demand::firstOrCreate(['no' => $no], [
                    'no' => $no,
                    'layout_id' => $id,
                    'payload' => $payload,
                    'created_by' => $request->has('is_child') && $request->get('is_child') ? Demand::where('no', $request->get('parent'))->first()->created_by : $userId,
                    'updated_by' => $userId,
                ]);

                // 新增customList
                $createdId = $DemandModel->id;
                data_set($customList, '*.demand_id', $createdId);
                CustomList::insert($customList);


                // $createdRow = Demand::where('id', $createdId)->first();

                // 上傳檔案
                $DemandModel = $this->demandUploadFile($requestForms, $DemandModel);


                // metadata
                $metadata = (object) [];
                if ($request->has('responseUrl'))
                    $metadata->responseUrl = $request->get('responseUrl');


                $DemandModel
                    ->setAttribute('metadata', $metadata)
                    ->save();

                // event(new DemandUpdated($DemandModel));
                DB::commit();
                return $DemandModel->id;
            }
            return 0;
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($requestForms);
            Log::error($e);
            return 0;
        }
    }
    /**
     * 建立需求單單號
     * @param mixed $layout DemandLayout
     * @return string
     */
    public function makeDemandNo($timezone, $layout): string
    {

        $code = isset($layout->payload['setting_code']) ? $layout->payload['setting_code'] : '';
        $preNo = 'AS' . Carbon::now($timezone)->format('Ymd') . $code;
        $naturalNo = str_pad(Demand::withTrashed()->where('no', 'like', '%' . $preNo . '%')->count() + 1, 5, '0', STR_PAD_LEFT);

        return $preNo . $naturalNo;
    }

    /**
     * 上傳檔案
     * @param  $requestForms $request->get('forms')
     * @param  $createdRow Demand
     * @param  $Upload UploadController
     *
     */
    public function demandUploadFile($requestForms, $createdRow)
    {
        //找附件把temp files move到單號資料夾
        $Upload = new UploadController;

        collect($requestForms)->each(function ($requestForm, $index) use ($createdRow, $Upload) {
            $doc = collect($requestForm)->where('type', 'document')->first();
            if (!empty($doc)) {
                $files = \Arr::where($doc['files'], function ($value, $key) {
                    return isset($value);
                });
                foreach ($files as $key => $file) {

                    $lastPath = '/demand/upload/' . $createdRow->no . '/' . $file['base_name'];
                    $uploadResult = $Upload->moveFiles($lastPath, $file['base_name']);
                    if ($uploadResult) {
                        $files[$key]['URL'] = '/storage' . $lastPath;
                        $files[$key]['path'] = '/' . $createdRow->no . '/' . $file['base_name'] . '/' . $file['name'];
                    }
                }
                $columnId = $doc['id'];
                $forms = $createdRow->payload->get('forms');
                $ii = 0;
                foreach ($forms[$index]['columns'] as $i => $column) {
                    if ($column['id'] == $columnId)
                        $ii = $i;
                }
                $forms[$index]['columns'][$ii]['files'] = $files;
                $createdRow->setAttribute('payload->forms', $forms);
            }
        });

        return $createdRow;
    }


    public function moveToDemandLog($id)
    {
        $demand = Demand::where('id', $id)->first();
        $dLogModel = DemandLog::create([
            'no' => $demand->no,
            'layout_id' => $demand->layout_id,
            // 為了關聯customList 所以加demand id
            'payload' => $demand->payload->merge(['demand_id' => $demand->id]),
            'created_by' => $demand->created_by,
            'updated_by' => $demand->updated_by,
            'created_at' => $demand->created_at
        ]);
        if ($dLogModel) {
            $demand->delete();
            RefreshMaterializedViewDemandQuery::handle();
            return 1;
        } else {
            return 0;
        }
    }

}
