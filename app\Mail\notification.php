<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class notification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    public $data;
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {

        $template = $this->view('emails.reserveMsg')
            ->subject($this->data['subject']);

        // 如果ics存在  
        if (isset($this->data['ics'])) {

            // 將時間轉成ics格式
            $msg = $this->data['msg'][0];
            $date = $msg['date'];
            $time = $msg['time'];
            $timeStart = substr($time[0], 0, 5);
            $timeEnd = substr($time[count($time) - 1], 6);
            foreach ($this->data['email'] as $email) {
                $this->data['ics']->VEVENT->add("ATTENDEE", "MAILTO:" . $email);
            }
            // 轉換為 UTC
            $this->data['ics']->VEVENT->DTSTART = Carbon::parse($date)
                ->setTimezone('Asia/Taipei')  // 先設為台北時區
                ->setTimeFromTimeString($timeStart)  // 設定台北時間
                ->setTimezone('UTC');  // 轉換為 UTC;

            $this->data['ics']->VEVENT->DTEND = Carbon::parse($date)
                ->setTimezone('Asia/Taipei')  // 先設為台北時區  
                ->setTimeFromTimeString($timeEnd)  // 設定台北時間
                ->setTimezone('UTC');


            // $this->data['ics']->VEVENT->DTSTART = '20250603T074300Z';
            // $this->data['ics']->VEVENT->DTEND = '20250603T080000Z';

            // 將ics轉成字串
            $this->data['ics'] = $this->data['ics']->serialize();

            // 添加ics附件  
            $template->attachData(
                $this->data['ics'],
                'reserve.ics',
                [
                    'mime' => 'text/calendar; charset=utf-8; method=REQUEST',
                ]
            );
        }
        return $template;
    }
}
