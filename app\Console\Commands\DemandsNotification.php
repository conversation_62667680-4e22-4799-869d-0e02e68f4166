<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Jobs\DemandNotify;

class DemandsNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demand:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send email notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info('DemandNotify start');
        DemandNotify::dispatch();
        Log::info('DemandNotify end');
        return 0;
    }
}
