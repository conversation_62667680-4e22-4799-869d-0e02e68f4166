<template>
  <div class="m-0">
    <div v-if="action == 1">
      <div class="p-2">
        <Checkbox class="my-1" :binary="true" v-model="can_insert" />
        <label>申請人可新增內容</label>
      </div>

      <div class="flex whitespace-nowrap">
        <table class="border-gray-300 border">
          <thead class="border-gray-300 border">
            <tr>
              <th v-for="(row, index) in form_setting" :key="index">
                <div class="flex p-5 border-gray-300 border-b flex items-center gap-2">
                  <p>輸入格式</p>
                  &emsp;
                  <RadioButton
                    @change="changeType(row, index)"
                    value="input"
                    v-model="row.type"
                    :id="'input_' + index"
                  />
                  <label :for="'input_' + index" class="cursor-pointer">文數字</label>&ensp;
                  <RadioButton
                    @change="changeType(row, index)"
                    value="number"
                    v-model="row.type"
                    :id="'number_' + index"
                  />
                  <label :for="'number_' + index" class="cursor-pointer">數字</label>&ensp;
                  <RadioButton
                    @change="changeType(row, index)"
                    value="money"
                    v-model="row.type"
                    :id="'money_' + index"
                  />
                  <label :for="'money_' + index" class="cursor-pointer">金額</label>&ensp;
                  <img
                    v-if="form_setting.length > 1"
                    @click="deleteCol(index)"
                    src="@images/icon/delete_enable.png"
                    alt=""
                  />
                </div>
              </th>
              <th @click="addCols" class="w-40 p-5 border-gray-300 border-l" />
            </tr>
            <tr>
              <td v-for="(row, index) in form_setting" :key="index">
                <div class="p-5 border-gray-300 border-b flex items-center gap-2">
                  <RadioButton :id="'mode_0_' + index" :value="0" v-model="row.mode" />
                  <label :for="'mode_0_' + index" class="cursor-pointer">不可填</label>&ensp;
                  <RadioButton :id="'mode_1_' + index" :value="1" v-model="row.mode" />
                  <label :for="'mode_1_' + index" class="cursor-pointer">申請人可填</label>&ensp;
                  <RadioButton :id="'mode_2_' + index" :value="2" v-model="row.mode" />
                  <label :for="'mode_2_' + index" class="cursor-pointer">審核人可填</label>&ensp;
                  <Checkbox :id="'is_must_' + index" :binary="true" v-model="row.is_must" />
                  <label :for="'is_must_' + index" class="cursor-pointer">必填</label>
                </div>
              </td>
              <td @click="addCols" class="w-40 p-5 border-gray-300 border-l">
                <label>新增標題</label>
              </td>
            </tr>
            <tr>
              <td v-for="(row, index) in form_setting" :key="index" class="p-5 border-gray-300 border-b">
                <div class="flex items-center gap-2">
                  <Checkbox :id="'frozen_' + index" :binary="true" v-model="row.frozen" />
                  <label :for="'frozen_' + index" class="cursor-pointer">固定欄位</label>
                  <Checkbox :id="'filterCriteria_' + index" :binary="true" v-model="row.filterCriteria" />
                  <label :for="'filterCriteria_' + index" class="cursor-pointer">無資料過濾</label>
                </div>
              </td>
              <td @click="addCols" class="w-40 p-5 border-gray-300 border-l" />
            </tr>
            <tr class="bg-gray-100">
              <td
                v-for="(row, index) in form_setting"
                :key="index"
                class="border-gray-300 border-b"
              >
                <input
                  v-model="row.name"
                  class="h-7 p-3 m-5 bg-gray-100"
                  placeholder="輸入標題"
                />
              </td>
              <td class="w-40 p-5 border-gray-300 border-b border-t" />
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, rowIndex) in custom_rows" :key="'row' + rowIndex">
              <td
                v-for="(col, colIndex) in custom_rows[rowIndex]"
                :key="'col' + colIndex"
                class="border-gray-300"
              >
                <InputText
                  v-if="form_setting[colIndex].type == 'input'"
                  v-model="row[colIndex]"
                  class="h-7 m-5"
                  placeholder="輸入內容"
                />
                <InputNumber
                  v-if="form_setting[colIndex].type == 'number'"
                  v-model="row[colIndex]"
                  class="h-7 m-5"
                  placeholder="輸入內容"
                />
                <InputNumber
                  v-if="form_setting[colIndex].type == 'money'"
                  v-model="row[colIndex]"
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  class="h-7 m-5"
                  placeholder="輸入內容"
                />
              </td>
              <td
                @click="deleteRow(rowIndex)"
                class="w-40 p-5 border-gray-300 border-l"
              >
                <img
                  v-if="custom_rows.length > 1"
                  src="@images/icon/delete_enable.png"
                  alt=""
                />
              </td>
            </tr>
            <tr>
              <td
                v-for="(col, colIndex) in form_setting"
                :key="'col_' + colIndex"
              >
                <p
                  v-if="col.type == 'money'"
                  class="font-extrabold p-5"
                  :class="col.type == 'number' ? 'text-transparent' : ''"
                >
                  ${{ FormatValue(moneyTotal(colIndex)) }}
                </p>
              </td>
              <td class="w-40 p-5 border-gray-300 border-l" />
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div v-if="action == 2">
      <DataTable
        v-if="custom_values.length"
        :value="custom_values"
        :scrollable="true"
        responsiveLayout="scroll"
        :editMode="edit == false ? '' : 'cell'"
        class="DataTable"
      >
        <Column
          v-for="(row, rowIndex) of form_setting"
          :field="rowIndex + ''"
          :header="formHeader(row)"
          :key="row.field"
          :styles="{ minWidth : '8.2rem' }"
          :frozen="row.frozen ? true&&shouldEnableFrozen : false"
          :className="!row.frozen&&project? 'cursor-grab active:cursor-grabbing': ''"
        >
          <template #body="slotProps">
            <div :class="formInput(row)" :id="autoFocus(row)">
              {{
                row.type == "money" || row.type==='number'
                  ? FormatValue(slotProps.data[slotProps.field])
                  : slotProps.data[slotProps.field]
              }}&nbsp;
            </div>
          </template>
          <template #editor="slotProps">
            <InputText
              class="w-full"
              v-if="
                signOff == 1
                  ? row.mode == 2 && signer.fill_column_list_ids?.includes(row.id) && row.type == 'input'
                  : row.mode == 1 && row.type == 'input'
              "
              v-model="slotProps.data[slotProps.field]"
              @input="changeInput(slotProps)"
            />
            <InputNumber
              class="w-full"
              v-else-if="
                signOff == 1
                  ? row.mode == 2 && signer.fill_column_list_ids?.includes(row.id) && row.type == 'money'
                  : row.mode == 1 && row.type == 'money'
              "
              v-model="slotProps.data[slotProps.field]"
              locale="en-US"
              mode="currency"
              currency="USD"
              @input="changeInput(slotProps)"
            />
            <InputNumber
              class="w-full"
              v-else-if="
                signOff == 1
                  ? row.mode == 2 && signer.fill_column_list_ids?.includes(row.id) && row.type == 'number'
                  : row.mode == 1 && row.type == 'number'
              "
              v-model="slotProps.data[slotProps.field]"
              @input="changeInput(slotProps)"
            />
            <label v-else>
              {{ row.type==='money'|| row.type==='number'? FormatValue(slotProps.data[slotProps.field]) : slotProps.data[slotProps.field] }}
            </label>
          </template>
        </Column>
        <Column
          v-if="mode == 'submit'"
          :field="form_setting.length + ''"
          header=""
          :key="form_setting.length"
          :styles="{'text-align': 'right', 'max-width': '72px'}"
        >
          <template #body="slotProps">
            <div @click="deleteRow(slotProps.index)">
              <img src="@images/icon/delete_enable.png" alt="" />
            </div>
          </template>
        </Column>
        <ColumnGroup type="footer">
          <Row>
            <Column
              v-for="(col, colIndex) of total_row"
              :key="colIndex"
              :footer="
                form_setting[colIndex].type == 'money'
                  ? FormatValue(col)
                  : null
              "
              :styles="{ padding: '1rem 1.5rem', height: '50px' }"
              :frozen="form_setting[colIndex].frozen ? true : false"
            />
            <Column v-if="mode=='submit'">
            </Column>
          </Row>
        </ColumnGroup>
      </DataTable>
      <div v-else>
        <span>該自訂表單無資料</span>
      </div>
    </div>
  </div>
</template>
<script>
import { nanoid } from 'nanoid'
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import ColumnGroup from "primevue/columngroup";
import Row from "primevue/row";
import RadioButton from "primevue/radiobutton";
import Checkbox from "primevue/checkbox";
import InputNumber from "primevue/inputnumber";
import InputText from "primevue/inputtext";

export default {
  components: {
    DataTable,
    Column,
    ColumnGroup,
    Row,
    RadioButton,
    Checkbox,
    InputNumber,
    InputText,
  },
  props: [
    "action",
    "mode",
    "dem_custom_list",
    "dem_form_setting",
    "dem_can_insert",
    "signOff",
    "edit",
    "columnId",
    "signer",
    "menu_index",
    "project", // 財務-專案付款功能用
    "sign_roles", // 財務-專案付款功能用
  ],
  data() {
    return {
      form_setting: [
        {
          id: this.columnId + "-" + 1,
          name: "",
          type: "input",
          mode: 0,
          is_must: false,
          frozen: false,
          filterCriteria: false
        },
      ],
      custom_rows: [
        [""],
        // [4544, 888, "455"],
      ],
      can_insert: false,
      custom_values: [],
      total_row: null,
      isDragging: false, // 財務-專案付款功能用
      startX: 0, // 財務-專案付款功能用
      scrollLeft: 0, // 財務-專案付款功能用
      seqeunce:  ['工程主管','財務主管','CPM','PD'], // 財務-專案付款功能用
      screenWidth: window.innerWidth
    };
  },
  watch: {
    custom_values: {
      handler: function (val, oldVal) {
        this.computedTotalRow();
      },
      deep: true,
    },
  },
  mounted() {
    if (this.dem_custom_list !== undefined) {
      if(this.project) {
        this.custom_rows = JSON.parse(JSON.stringify(this.dem_custom_list)) // 深拷
        // 寫入上一審核人之金額
        if(this.signOff===1) {this.custom_rows = this.custom_rows.map((row) => {
          return row.map((col,col_index) => {
            if(this.signer.fill_column_list_ids?.slice(0,1).includes(this.dem_form_setting[col_index].id)) {
              const current = this.seqeunce.indexOf(this.signer.self_name)
              const former = this.seqeunce.indexOf(this.sign_roles[this.sign_roles.indexOf(this.signer.self_name)-1])
              const gap = current - former
              return row[col_index-gap*2]
            }
            return col
          })
        })}

        // 剔除流程中沒有的審核主管欄位
        this.custom_rows = this.custom_rows.map(row => {
          let newRow = row.slice(0,12)
          this.seqeunce.forEach((element,index) => {
            if(this.sign_roles.includes(element)) {
              newRow.push(...row.slice(12+2*index,14+2*index))
            }
          })
          return newRow
        })
      } else {
        this.custom_rows = this.dem_custom_list;
      }
    }

    if (this.dem_form_setting !== undefined) {
      if(this.project) {
        // 剔除流程中沒有的審核主管欄位
        this.form_setting = JSON.parse(JSON.stringify(this.dem_form_setting)).slice(0,12)
        this.seqeunce.forEach((element,index) => {
          if(this.sign_roles.includes(element)) {
            this.form_setting.push(...this.dem_form_setting.slice(12+2*index,14+2*index))
          }
        })
      } else {
        this.form_setting = this.dem_form_setting;
      }
      this.computedTotalRow();
    }

    if (this.dem_can_insert !== undefined) {
      this.can_insert = this.dem_can_insert;
    }

    this.rowForDataTable();

    if(this.project) {
      const dragWarpper = document.querySelector('.p-datatable-wrapper')
      dragWarpper.addEventListener('mousedown', this.startDrag)
      dragWarpper.addEventListener('mousemove', this.draging)
      dragWarpper.addEventListener('mouseup', this.stopDrag)
      dragWarpper.addEventListener('mouseleave', this.stopDrag)

      this.$nextTick(() => {
        // 設置滾動容器的滾動位置為最右邊
        const scrollContainer = this.$el.querySelector('.p-datatable-wrapper');
        if (scrollContainer) {
          scrollContainer.scrollLeft = scrollContainer.scrollWidth - scrollContainer.clientWidth;
        }

        //點亮第一個使用者須填寫的欄位
        const elementToClick = document.getElementById('autoFocus');
        const clickEvent = new MouseEvent('click', {
          bubbles: true,  // 事件是否冒泡
          view: window,  // 事件的關聯視窗
        });
        if (elementToClick) {
          elementToClick.dispatchEvent(clickEvent);
        }
      })
    }

    window.addEventListener('resize', this.updateScreenWidth);
  },
  onUnmounted() {
    window.removeEventListener('resize', this.updateScreenWidth);
  },
  methods: {
    formHeader(row) {
      if (this.mode == "signing") {
        if (
          this.signer !== undefined &&
          this.signer.fill_column_list_ids?.includes(row.id) &&
          row.mode == 2 &&
          row.is_must
        ) {
          return row.name + "*";
        } else {
          return row.name;
        }
      } else {
        if (row.mode == 1 && row.is_must) {
          return row.name + "*";
        } else {
          return row.name;
        }
      }
    },
    formInput(row) {
      let className = ""
      if(this.mode == "signing" && this.signer.fill_column_list_ids?.includes(row.id) && row.mode == 2) {
        className += " border border-gray-200 w-full rounded-md p-3"
      } else if(this.mode == "submit" && row.mode == 1) {
        className += " border border-gray-200 w-full rounded-md p-3"
      }

      return className
    },
    autoFocus(row) {
      if(this.mode == "signing" && this.signer.fill_column_list_ids?.includes(row.id) && row.mode == 2) {
        return "autoFocus"
      }
      return ''
    },
    FormatValue(value) {
      const formatter = new Intl.NumberFormat("en-US", {
        currency: "USD",
      });
      if (value == null) {
        return (value = 0);
      } else {
        return (value = formatter.format(value));
      }
    },
    rowForDataTable() {
      this.custom_values = this.custom_rows.map((row, index) => {
        return Object.assign({}, row);
      });
    },
    changeInput(sp) {
      this.$emit('fill')
      let valueRowKey = sp.index;
      let valueColKey = Number(sp.field);
      if(this.project) {
        const data ={
          seq: [valueRowKey, valueColKey%2===1? 12+2*this.seqeunce.findIndex(el => el===this.signer.self_name)+1: 12+2*this.seqeunce.findIndex(el => el===this.signer.self_name)],
          value: $event
        }
        this.$emit('updateCL', data)
      }
      this.custom_values[valueRowKey][valueColKey] = Object.values(sp.data)[
        valueColKey
      ];
      this.custom_rows[valueRowKey][valueColKey] = Object.values(
        this.custom_values[valueRowKey]
      )[valueColKey];
    },
    addCols() {
      this.form_setting.push({
        id: this.columnId + "-" + nanoid(),
        name: "",
        type: "input",
        mode: 0,
        is_must: false,
        frozen: false,
        can_insert: false,
      });
      this.custom_rows.forEach((row, index) => {
        this.custom_rows[index].push("");
      });
    },
    deleteRow(index) {
      if (this.custom_rows.length > 1) {
        this.custom_rows.splice(index, 1);
        this.custom_values.splice(index, 1);
      }
    },
    deleteCol(index) {
      this.form_setting.splice(index, 1);
      this.custom_rows.forEach((row, r_index) => {
        this.custom_rows[r_index].splice(index, 1);
      });
    },
    changeType(row, index) {
      if (row.type == "input") {
        this.custom_rows.forEach((row, r_index) => {
          this.custom_rows[r_index][index] = "";
        });
      } else {
        this.custom_rows.forEach((col, r_index) => {
          this.custom_rows[r_index][index] = 0;
        });
      }
    },
    finallyTotal(cols) {
      return cols.reduce((a, m) => {
        return a + m || 0;
      });
    },
    moneyTotal(index) {
      let col_values = this.custom_rows.map((row) => {
        return row[index];
      });
      return this.finallyTotal(col_values);
    },
    computedTotalRow() {
      if(this.custom_rows[0]) {
        this.total_row = this.custom_rows[0].map((col, index) => {
          return this.custom_rows.reduce((acc, array) => {
            if (this.form_setting[index].type == "money") {
              return acc + array[index];
            } else {
              return null;
            }
          }, 0)
        })
      }
    },
    startDrag(event) {
      const tablWarapper = document.querySelector('.p-datatable-wrapper')
      this.isDragging = true;
      this.startX = event.clientX;
      this.scrollLeft = tablWarapper.scrollLeft;
    },
    draging(event) {
      if (this.isDragging) {
        const tablWarapper = document.querySelector('.p-datatable-wrapper')
        const deltaX = event.clientX - this.startX;
        tablWarapper.scrollLeft = this.scrollLeft - deltaX;
      }
    },
    stopDrag() {
      this.isDragging = false;
    },
    updateScreenWidth() {
      this.screenWidth = window.innerWidth;
    }
  },
  computed: {
    shouldEnableFrozen() {
      return this.screenWidth > 768
    }
  },
};
</script>
<style>
.DataTable tfoot td:nth-of-type(3) {
  left: 256px;
}

.DataTable td, .DataTable th {
  padding: 6px !important;
}

.DataTable .p-inputnumber {
  width: 100%;
}
</style>
