<?php

namespace App\Modules\Demand\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\Demand\Models\Employee;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
    }

    public function fetchEmployeeData(Request $request)
    {
        if (empty($this->user_id))
            return null;

        $employee = Employee::where('id', $this->user_id)
            ->with('orgs')
            ->first();
        //代理人
        $agentName = '';
        if (isset($employee->payload['agent'])) {
            $agentName = Employee::where('id', $employee->payload['agent'])->first();
            $agentName = isset($agentName->payload['name']) ? $agentName->payload['name'] : '';
        };
        //找主管
        $org = $employee->orgs[0];
        $manager = Employee::where('id', $org->payload->get('manager'))
            ->with('orgs')
            ->first();
        if (isset($manager->id, $employee->id) && $manager->id == $employee->id) {
            $Role = new RoleController;
            $managerId = $Role->findLevleManager($employee->id, 0);
            $parentManager = Employee::where('id', $managerId)->first();
            $managerName = empty($parentManager)?'':$parentManager->payload->get('name');
        } else {
            $managerName = $manager->payload->get('name');
        };

        $payload = $employee->payload;
        $result = [
            'id' => $this->user_id,
            'agent' => $agentName,
            'dep' => count($employee->orgs) > 0 ? $employee->orgs[0]->payload->get('fullname') : '',
            'manager' => $managerName,
            'name' => $payload['name'],
            'no' => $payload['employee_number'],
            'title' => $payload['job_title'],
            'photo' => $employee->payload->get('imgUrl') != null ? $employee->payload->get('imgUrl') : '/storage/demand/image/nobody.jpg',
            'agentSign' => isset($employee->metadata['has_agent']) ? $employee->metadata['has_agent'] : false,
        ];

        return $result;
    }

    //* 代簽核開關 @params has_agent:0/1
    public function changeUserAgentSwitch(Request $request)
    {
        $employee = Employee::where('id', $this->user_id)->first();
        if ($request->get('has_agent')) {
            $employee->setAttribute('metadata->has_agent', true);
        } else {
            $employee->setAttribute('metadata->has_agent', false);
        }
        $employee->save();
        return 1;
    }

    //* 改代理人id
    public function changeUserAgentPerson(Request $request)
    {
        $id = $request->get('id');
        if (!$id)
            return 0;
        $user = Employee::where('id', $this->user_id)->first();
        $user->setAttribute('payload->agent', $id);
        $user->save();
        return 0;
    }
}
