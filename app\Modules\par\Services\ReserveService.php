<?php

namespace App\Modules\par\Services;

use App\Exceptions\ParException;
use App\Modules\Demand\Controllers\NotificationController;
use App\Modules\par\Controllers\CommonController;
use App\Modules\par\Models\Apparatus;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Models\Reserve;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use PhpParser\Node\Expr\Array_;
use Carbon\Carbon;
use Sabre\VObject\Component\VCalendar;

class ReserveService extends PublicService
{
    protected $company_id;
    protected $user_id;
    protected $timezone;

    public function __construct()
    {
        $this->company_id = Session::get('CompanyId');
        $this->user_id = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
    }
    /**
     * 新增預約通知
     * @param array $notifyInfo
     * @param int $reserveId
     * @param carbon $date reserve date
     * @param int $time reserve time code id
     */
    public function reserveNotify($notifyInfo)
    {
        // 設定通知的日期時間，會比最早預約時間早一小時
        $code = CommonController::fetchReserveTimePeriod();
        $time = explode(':', substr($code->firstWhere('id', $notifyInfo['time'][0])->name, 0, 5));
        //  $date->setTimezone($this->timezone);
        $notifyInfo['date']->addHours($time[0]);
        $notifyInfo['date']->addMinutes($time[1]);
        // 新增預約通知
        // 假設08:30的預約，07:30~07:59
        // 假設09:00的預約，08:00~08:29
        NotificationController::createReserveNotify(
            $notifyInfo['reserveId'],
            $notifyInfo['date']->copy()->addMinutes(-60)->toISOString(),
            $notifyInfo['date']->copy()->addMinutes(-31)->toISOString()
        );
    }

    public function validatorPostulate($form)
    {
        $rules = [
            'pa_id' => 'required|exists:App\Modules\par\Models\Postulate,id',
            'name' => 'required',
            'date' => 'required|date',
            'time_ids' => 'required|array',
            'reason' => 'required',
            'apparatus' => 'array',
        ];

        $msg = [
            'pa_id.required' => '請輸入設施!',
            'name.required' => '請輸入設施!',
            '*.exists' => '該項目不存在，請重新整理!',
            'date.required' => '請輸入預約日期!',
            'date.date' => '預約日期輸入錯誤!',
            'time_ids.required' => '請輸入預約時段!',
            'time_ids.array' => '預約時段輸入錯誤!',
            'reason.required' => '請輸入預約事由!',
            'apparatus.array' => '設備支援錯誤!',
        ];
        $validator = Validator::make($form, $rules, $msg);

        return $validator;
    }

    public function validatorApparatues($form)
    {
        $rules = [
            'pa_id' => 'required|exists:App\Modules\par\Models\Apparatus,id',
            'name' => 'required',
            'amount' => 'required|numeric',
            'date' => 'required|date',
            'time_ids' => 'required|array',
            'reason' => 'required'
        ];

        $msg = [
            'pa_id.required' => '請輸入設備!',
            'name.required' => '請輸入設備!',
            '*.exists' => '該項目不存在，請重新整理!',
            'amount.required' => '請輸入數量!',
            'amount.numeric' => '數量請輸入數字!',
            'date.required' => '請輸入預約日期!',
            'date.date' => '預約日期輸入錯誤!',
            'time_ids.required' => '請輸入預約時段!',
            'time_ids.array' => '預約時段輸入錯誤!',

            'reason.required' => '請輸入預約事由!',
        ];
        $validator = Validator::make($form, $rules, $msg);

        return $validator;
    }
    public function validatorCancel($form)
    {
        $rules = [
            'id' => [
                'required',
                Rule::exists('App\Modules\par\Models\Reserve', 'id')
                    ->where(function ($query) use ($form) {
                        is_array($form['id']) ?
                            $query->wherein('id', $form['id']) :
                            $query->where('id', $form['id']);
                    }),
            ],
            'cancel_type' => 'required|boolean',
        ];


        $msg = [
            'id.required' => '請輸入預約!',
            'id.exists' => '項目不存在，請重新整理!',
            'cancel_type.required' => '請選擇類別!',
            'cancel_type.boolean' => '系統錯誤!',
        ];
        $validator = Validator::make($form, $rules, $msg);

        return $validator;
    }

    /**
     * 檢查預約時間
     * @param string $type apparatus|postulate
     * @param int $id pa_id
     * @param string $date reserve date
     */
    protected function fetchCheckTimeReserve($type, $id, $date)
    {
        $reserve = Reserve::where('type', $type)
            ->where('pa_id', $id)
            ->date($date)
            ->notRelease()
            ->lockForUpdate()
            ->get();

        return $reserve;
    }

    /**
     * 檢查每個時段的數量是否充足
     * @param string $type apparatus|postulate
     * @param int $id pa_id
     * @param string $date reserve date
     * @param array $timeIds reserve times
     * @return array errortimes
     */
    public function checkPostulateTimeReserve($type, $id, $date, $timeIds)
    {

        $reserve = $this->fetchCheckTimeReserve($type, $id, $date);

        if ($reserve->count() == 0)
            return [];

        $errortime = [];
        foreach ($timeIds as $timeId) {
            if ($reserve->isTimeinReserves($timeId)) {
                array_push($errortime, self::idToTimePeriod($timeId));
            }
        }

        return $errortime;
    }

    // 修改預約例外
    public function reCheckPostulateTimeReserve($type, $pa_id, $date, $timeIds, $id)
    {
        $reserve = $this->fetchCheckTimeReserve($type, $pa_id, $date)->where('id', '!=', $id);
        if ($reserve->count() == 0)
            return [];

        $errortime = [];
        foreach ($timeIds as $timeId) {
            if ($reserve->isTimeinReserves($timeId)) {
                array_push($errortime, self::idToTimePeriod($timeId));
            }
        }

        return $errortime;
    }

    /**
     * @param string $type apparatus|postulate
     * @param int $id pa_id
     * @param string $date reserve date
     * @param array $timeIds reserve times
     * @param int $amount
     */
    public function checkApparatusTimeReserve($type, $id, $date, $timeIds, $amount)
    {
        $reserve = $this->fetchCheckTimeReserve($type, $id, $date);

        $apparatus = Apparatus::where('id', $id)->select('payload->amount as amount')->first();
        if ($reserve->count() == 0) {
            return $amount > $apparatus->amount ? self::idToTimePeriod($timeIds) : [];
        }
        $errortime = [];
        foreach ($timeIds as $timeId) {
            $reserveSum = $reserve->sumReservesAmount($timeId);
            // 所有預約的數量+準備預約的數量 > 實際數量
            if ($reserveSum + $amount > $apparatus->amount) {
                array_push($errortime, self::idToTimePeriod($timeId));
            }
        }

        return $errortime;
    }

    /**
     * 設施預約中用設備的類別確認可預約的設備
     * @param string $date reserve date
     * @param array $timeIds reserve tims_id
     * @param string $type apparatus type
     */
    public function checkReservesApparatusWithType($date, $timeIds, $type)
    {
        $apparatus = Apparatus::with([
            'reserve' => function ($query) use ($date) {
                $query->date($date)
                    ->notRelease();
            }
        ])
            ->company()
            ->isOpen()
            ->where('payload->type', $type)
            ->select('id', 'name', 'payload->amount as amount')
            ->get();
        if ($apparatus->isEmpty())
            return response('null')->header('Content-Type', 'application/json');
        $result = [];

        $result = $apparatus
            ->filter(function ($item) use ($timeIds) {
                // 確認所有時段都是可以預約的，其中一個時段不行，就不可以預約
                foreach ($timeIds as $timeId) {
                    $reserveSum = $item->reserve->sumReservesAmount($timeId);
                    // 實際數量 < 所有預約的數量 + 設施內的設備=1
                    if ($item->amount < $reserveSum + 1)
                        return false;
                }
                return true;
            })
            ->map(function ($item) {
                return ['id' => $item->id, 'name' => $item->name];
            });
        return $result;
    }

    public function releaseReserve($id)
    {
        $reserve = Reserve::whereNull('metadata->release')->where('id', $id)->first();
        if (!isset($reserve))
            throw new ParException('預約已釋出，請重整');
        if (
            !empty($reserve->payload['apparatus']) &&
            count(array_column($reserve->payload['apparatus'], 'id')) > 0
        ) {
            Reserve::postulateAp($id)
                ->update([
                    'metadata' => ['release' => true]
                ]);
        }
        $reserve->metadata = ['release' => true];

        $reserve->save();



        return 1;
    }

    public function releaseReserveByIds($id)
    {
        $reserve = Reserve::whereNull('metadata->release')->wherein('id', $id)->get();
        if (!isset($reserve))
            throw new ParException('預約已釋出，請重整');

        $reserve->each(function ($value) {
            $value->metadata = ['release' => true];
            if (
                $value->type == 'postulate' &&
                !empty($value->payload['apparatus']) &&
                count(array_column($value->payload['apparatus'], 'id')) > 0
            ) {
                Reserve::postulateAp($value->id)
                    ->update([
                        'metadata' => ['release' => true]
                    ]);
            }
            $value->save();
        });


        return 1;
    }

    public function deleteReserve($id)
    {
        $reserve = Reserve::find($id);
        if (!isset($reserve))
            throw new ParException('預約已刪除，請重整');

        if (
            !empty($reserve->payload['apparatus']) &&
            count(array_column($reserve->payload['apparatus'], 'id')) > 0
        ) {
            Reserve::postulateAp($id)->delete();
        }
        $reserve->delete();

        $reserve->save();
        return 1;
    }

    /**
     * 新增ics
     * @param string $name
     * @param string $date reserve date
     * @param string $reason reserve reason
     * @param string $remark reserve remark
     */
    public function newIcs($name, $date, $reason, $remark)
    {
        $ics = new VCalendar();

        // 新增事件
        $ics->add('VEVENT', [
            // 【會議室預約】會議室登記日期(空格半形)預約事由
            'SUMMARY' => '【會議室預約】' . $date->setTimezone('Asia/Taipei')->format('Y/m/d') . ' ' . $reason,
            'DTSTART' => $date->toISOString(),
            'DTEND' => $date->toISOString(),
            'DESCRIPTION' => $remark,
            'LOCATION' => $name,
        ]);

        // 新增提醒
        $ics->VEVENT->add('VALARM', [
            'TRIGGER' => '-PT15M',
            'ACTION' => 'DISPLAY',
            // 'DESCRIPTION' => '會議將在5分鐘後開始',
        ]);

        // 必須存在，否則行事曆不會自動加入ics
        $ics->add('METHOD', 'REQUEST');

        return $ics;
    }
}
