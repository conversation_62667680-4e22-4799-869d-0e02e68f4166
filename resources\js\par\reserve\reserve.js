import '@/bootstrap.js';

import Banner from "@/demand/common/Banner.vue";
import Apparatus from "./apparatus.vue";
import Postulate from "./postulate.vue";

const app = new Vue({
    el: "#content",
    components: {
        Banner,
        Apparatus,
        Postulate,
    },
    data: {
        titles: ["設施預約", "設備預約"],
        currentTab: 0,
        action: 0,
        reserve: last_section == 0 ? 0 : 1,
        loading: false,
    },

    mounted() {
        // if (isDevelopment) {
        this.titles = [last_section == 0 ? "設備預約" : "設施預約"];
        // }
    },
});
