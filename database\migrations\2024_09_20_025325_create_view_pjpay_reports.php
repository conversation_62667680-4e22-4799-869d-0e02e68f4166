<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $sql = <<<SQL
            -- public.pjpay_reports source
            -- demand union demand_log， sql差在join custom_lists而已
            CREATE OR REPLACE VIEW public.pjpay_reports
            AS SELECT d.id,
                d.no,
                concat(((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 1) ->> 'value'::text, '_'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 2) ->> 'value'::text) AS pkey,
                d.created_by,
                jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text AS columns,
                cl.list,
                jsonb_build_object('hash_id', md5(concat(to_json(cl.list), d.payload ->> 'startYm'::text, d.payload ->> 'endYm'::text, d.payload ->> 'pbatchno'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 1) ->> 'value'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 2) ->> 'value'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 3) ->> 'value'::text)), 'sign_roles', sub_sign_roles.sign_roles, 'status', (d.payload ->> 'status'::text)::integer, 'startYm', d.payload ->> 'startYm'::text, 'endYm', d.payload ->> 'endYm'::text, 'pbatchno', d.payload ->> 'pbatchno'::text, 'epjacc', ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 1) ->> 'value'::text, 'epjno', ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 2) ->> 'value'::text, 'epjna', ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 3) ->> 'value'::text) AS payload,
                d.updated_by,
                d.created_at,
                d.updated_at
            FROM demands d
                LEFT JOIN ( SELECT t.demand_id,
                        x.value AS list
                    FROM ( SELECT cl_1.demand_id,
                                jsonb_agg(cl_1.acc_name ORDER BY (cl_1.sort::integer)) AS name,
                                jsonb_array_elements(cl_1.list) AS value
                            FROM ( SELECT cu.demand_id,
                                        jsonb_array_elements(cu.payload -> 'form_setting'::text) -> 'acc_name'::text AS acc_name,
                                        split_part(jsonb_array_elements(cu.payload -> 'form_setting'::text) ->> 'id'::text, '-'::text, 2) AS sort,
                                        cu.list
                                    FROM custom_lists cu
                                    WHERE cu.deleted_at IS NULL AND (cu.demand_id IN ( SELECT d2.id
                                            FROM demands d2
                                            WHERE d2.deleted_at IS NULL AND (d2.payload ->> 'unique_key'::text) = 'acc_only'::text))) cl_1
                            GROUP BY cl_1.demand_id, cl_1.list) t
                        CROSS JOIN LATERAL ( SELECT jsonb_object_agg(k.ky, v.value) AS value
                            FROM jsonb_array_elements_text(t.name) WITH ORDINALITY k(ky, idx)
                                JOIN jsonb_array_elements(t.value) WITH ORDINALITY v(value, idx) ON k.idx = v.idx) x) cl ON cl.demand_id = d.id
                LEFT JOIN ( SELECT splite.id,
                        jsonb_agg(jsonb_build_object('id', (splite.sss ->> 'id'::text)::integer, 'self_name', splite.sss ->> 'self_name'::text, 'role_id', (splite.sss ->> 'role_id'::text)::integer, 'raw_time', splite.sss ->> 'raw_time'::text, 'timestamp', splite.sss ->> 'timestamp'::text, 'isRepresent', (splite.sss ->> 'isRepresent'::text)::boolean, 'apply_status', (splite.sss ->> 'apply_status'::text)::integer, 'remark', splite.sss ->> 'remark'::text) ORDER BY ((splite.sss ->> 'id'::text)::integer)) AS sign_roles
                    FROM ( SELECT d_1.id,
                                jsonb_array_elements(d_1.payload -> 'sign_roles'::text) AS sss
                            FROM demands d_1
                            WHERE d_1.deleted_at IS NULL AND (d_1.payload ->> 'unique_key'::text) = 'acc_only'::text) splite
                    GROUP BY splite.id) sub_sign_roles ON sub_sign_roles.id = d.id
            WHERE d.deleted_at IS NULL AND (d.payload ->> 'unique_key'::text) = 'acc_only'::text
            UNION
            SELECT d.id,
                d.no,
                concat(((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 1) ->> 'value'::text, '_'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 2) ->> 'value'::text) AS pkey,
                d.created_by,
                jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text AS columns,
                cl.list,
                jsonb_build_object('hash_id', md5(concat(to_json(cl.list), d.payload ->> 'startYm'::text, d.payload ->> 'endYm'::text, d.payload ->> 'pbatchno'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 1) ->> 'value'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 2) ->> 'value'::text, ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 3) ->> 'value'::text)), 'sign_roles', sub_sign_roles.sign_roles, 'status', (d.payload ->> 'status'::text)::integer, 'startYm', d.payload ->> 'startYm'::text, 'endYm', d.payload ->> 'endYm'::text, 'pbatchno', d.payload ->> 'pbatchno'::text, 'epjacc', ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 1) ->> 'value'::text, 'epjno', ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 2) ->> 'value'::text, 'epjna', ((jsonb_array_elements(d.payload -> 'forms'::text) -> 'columns'::text) -> 3) ->> 'value'::text) AS payload,
                d.updated_by,
                d.created_at,
                d.updated_at
            FROM demand_logs d
                LEFT JOIN ( SELECT t.demand_id,
                        x.value AS list
                    FROM ( SELECT cl_1.demand_id,
                                jsonb_agg(cl_1.acc_name ORDER BY (cl_1.sort::integer)) AS name,
                                jsonb_array_elements(cl_1.list) AS value
                            FROM ( SELECT cu.demand_id,
                                        jsonb_array_elements(cu.payload -> 'form_setting'::text) -> 'acc_name'::text AS acc_name,
                                        split_part(jsonb_array_elements(cu.payload -> 'form_setting'::text) ->> 'id'::text, '-'::text, 2) AS sort,
                                        cu.list
                                    FROM custom_lists cu
                                    WHERE cu.deleted_at IS NULL AND (cu.demand_id IN ( SELECT (d2.payload ->> 'demand_id'::text)::integer AS id
                                            FROM demand_logs d2
                                            WHERE d2.deleted_at IS NULL AND (d2.payload ->> 'unique_key'::text) = 'acc_only'::text))) cl_1
                            GROUP BY cl_1.demand_id, cl_1.list) t
                        CROSS JOIN LATERAL ( SELECT jsonb_object_agg(k.ky, v.value) AS value
                            FROM jsonb_array_elements_text(t.name) WITH ORDINALITY k(ky, idx)
                                JOIN jsonb_array_elements(t.value) WITH ORDINALITY v(value, idx) ON k.idx = v.idx) x) cl ON cl.demand_id = ((d.payload ->> 'demand_id'::text)::integer)
                LEFT JOIN ( SELECT splite.id,
                        jsonb_agg(jsonb_build_object('id', (splite.sss ->> 'id'::text)::integer, 'self_name', splite.sss ->> 'self_name'::text, 'role_id', (splite.sss ->> 'role_id'::text)::integer, 'raw_time', splite.sss ->> 'raw_time'::text, 'timestamp', splite.sss ->> 'timestamp'::text, 'isRepresent', (splite.sss ->> 'isRepresent'::text)::boolean, 'apply_status', (splite.sss ->> 'apply_status'::text)::integer, 'remark', splite.sss ->> 'remark'::text) ORDER BY ((splite.sss ->> 'id'::text)::integer)) AS sign_roles
                    FROM ( SELECT d_1.id,
                                jsonb_array_elements(d_1.payload -> 'sign_roles'::text) AS sss
                            FROM demand_logs d_1
                            WHERE d_1.deleted_at IS NULL AND (d_1.payload ->> 'unique_key'::text) = 'acc_only'::text) splite
                    GROUP BY splite.id) sub_sign_roles ON sub_sign_roles.id = d.id
            WHERE d.deleted_at IS NULL AND (d.payload ->> 'unique_key'::text) = 'acc_only'::text;
        SQL;

        \DB::statement($sql);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pjpay_reports');
    }
};
