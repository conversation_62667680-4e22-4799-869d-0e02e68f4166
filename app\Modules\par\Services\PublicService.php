<?php

namespace App\Modules\par\Services;

use App\Mail\DemandsMail;
use App\Modules\par\Controllers\CommonController;
use App\Modules\par\Models\Apparatus;
use App\Modules\par\Models\Postulate;
use App\Modules\par\Models\Reserve;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use App\Modules\Demand\Controllers\NotificationController;
use App\Modules\par\Events\notificationEvent;
use Illuminate\Support\Facades\Log;

class PublicService
{
    // protected $company_id;
    protected $user_id;
    protected $timezone;
    protected $com;


    public function __construct()
    {
        $this->timezone = Session::get('timezone');
    }

    public static function getEmail($employees)
    {
        $isEmp = isset($employees->first()->payload);
        $emails = $employees
            ->when(
                $isEmp,
                function ($query) {
                    return $query->pluck('payload->email');
                },
                function ($query) {
                    return $query->pluck('email');
                }
            )
            ->unique()
            ->filter(function ($item) {
                if (!filter_var($item, FILTER_VALIDATE_EMAIL))
                    Log::info($item);
                return filter_var($item, FILTER_VALIDATE_EMAIL);
            })->map(function ($item) {
                return trim($item);
            });
        return $emails;
    }

    /**
     * @param string $type apparatus|postulate
     * @return Model
     */
    public function fetchModel($type)
    {
        switch ($type) {
            case 'apparatus':
                $model = Apparatus::company();
                break;
            case 'postulate':
                $model = Postulate::company();
                break;
        }
        return $model;
    }
    /**
     * 取得現在時間段的代號id
     */
    public function getNowTimePeriodId()
    {
        $nowTime = now()->setTimezone($this->timezone)->format('Y/m/d H:i');
        $morning = carbon::today()->setTimezone($this->timezone)->set('hour', 8)->format('Y/m/d H:i');
        $evening = Carbon::today()->setTimezone($this->timezone)->set('hour', 19)->format('Y/m/d H:i');

        // 因為 function checkReserve 檢查預約是否可刪除，所以回傳不同值
        if ($nowTime <  $morning) return -1;
        else if ($nowTime >  $evening) return 100;
        $timePeriod = CommonController::fetchReserveTimePeriod();
        $today = substr($nowTime, 0, 11);
        $nowPeriod = $timePeriod->first(function ($value, $key) use ($today, $nowTime) {
            $time = explode('-', $value->name);
            return $today . $time[0] <= $nowTime &&
                $nowTime < $today . $time[1];
        });

        return  $nowPeriod->id;
    }
    /**
     *
     * @param array|string $periodId
     */
    public static function idToTimePeriod($periodId)
    {
        $code = CommonController::fetchReserveTimePeriod();

        return is_array($periodId) ?
            $code->wherein('id', $periodId)->pluck('name') :
            $code->firstWhere('id', $periodId)->name;
    }

    /**
     * 確認是否還存在預約
     * @param string $type apparatus|postulate
     * @param int|array $id pa_id
     * @return boolean
     */
    public function checkReserve($type, $id, $includeNow = false)
    {
        // 取得現在時間區段的code id
        $timeId  = $this->getNowTimePeriodId();
        $today = today(Session::get('timezone'))->toISOString();
        // $today = today('asia/taipei')->toISOString();
        // 要判斷現在的時間之後是否還有預約
        $reserve = Reserve::with('employee')
            ->where('type', $type)
            ->when(
                is_array($id),
                function ($query) use ($id) {
                    return $query->wherein('pa_id', $id);
                },
                function ($query) use ($id) {
                    return  $query->where('pa_id', $id);
                }
            )
            ->liveReserve($today)
            ->notRelease()
            ->get();

        // 刪除預約和刪除通知的fun會用到此collection，所以剔除正在進行中的預約
        if (!$includeNow) {
            $reserve = $reserve
                ->reject(function ($item) use ($today, $timeId) {

                    if ($item->payload['date'] > $today)  return false;
                    elseif ($timeId > max($item->payload['time'])) return true;
                    else
                        return
                            min($item->payload['time']) <= $timeId &&
                            $timeId <= max($item->payload['time']);
                });
        }
        if ($reserve->isEmpty()) return ['hasNotStarted' => false];

        $hasNotStarted = $reserve
            ->every(function ($item) use ($today, $timeId, $includeNow) {
                if ($item->payload['date'] > $today) return true;
                elseif ($includeNow && in_array($timeId, $item->payload['time'])) return true;
                else if ($timeId < min($item->payload['time'])) return true;

                return false;
                // 判斷預約最後時段是否大於現在時段
                // return max($item->payload['time']) >= $timeId ? false : true;
            });

        return [
            'reserve' => $reserve,
            'hasNotStarted' => $hasNotStarted
        ];
    }
}
